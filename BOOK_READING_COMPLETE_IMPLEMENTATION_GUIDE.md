# Complete Book Reading Implementation Guide for Noti

**Document Version**: 2.0  
**Created**: July 18, 2025  
**Status**: Production Implementation Guide

## 🚨 CRITICAL ISSUES REQUIRING IMMEDIATE ATTENTION

### 1. SECURITY VULNERABILITIES (CRITICAL - FIX IMMEDIATELY)

#### XSS Vulnerability in BookReader.vue
**File**: `src/components/BookReader.vue`  
**Line**: 84  
**Issue**: Direct HTML injection without sanitization

```vue
<!-- CURRENT DANGEROUS CODE -->
<div v-else-if="currentPageContent" class="page-content" v-html="currentPageContent"></div>
```

**IMMEDIATE FIX REQUIRED**:
```bash
# Install DOMPurify
npm install dompurify @types/dompurify
```

```vue
<!-- SAFE IMPLEMENTATION -->
<div v-else-if="currentPageContent" class="page-content" v-html="sanitizedContent"></div>
```

```typescript
// Add to script setup
import DOMPurify from 'dompurify'

const sanitizedContent = computed(() => {
  if (!currentPageContent.value) return ''
  return DOMPurify.sanitize(currentPageContent.value, {
    ALLOWED_TAGS: ['p', 'div', 'span', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'em', 'strong', 'i', 'b', 'u', 'br', 'img', 'a', 'ul', 'ol', 'li', 'blockquote', 'pre', 'code'],
    ALLOWED_ATTR: ['src', 'alt', 'href', 'title', 'class', 'id'],
    FORBID_SCRIPT: true,
    STRIP_COMMENTS: true
  })
})
```

#### HTML Sanitization in Book Parser
**File**: `electron/main/lib/book-parser.ts`  
**Lines**: 140-150  
**Issue**: Raw HTML stored without sanitization

```typescript
// CURRENT DANGEROUS CODE (Line 141)
const htmlContent = section.document.body.innerHTML || '';

// REQUIRED FIX
import DOMPurify from 'dompurify'
import { JSDOM } from 'jsdom'

// Create server-side DOM for sanitization
const window = new JSDOM('').window
const purify = DOMPurify(window as any)

// Replace line 141 with:
const htmlContent = purify.sanitize(section.document.body.innerHTML || '', {
  ALLOWED_TAGS: ['p', 'div', 'span', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'em', 'strong', 'i', 'b', 'u', 'br', 'img', 'a', 'ul', 'ol', 'li', 'blockquote', 'pre', 'code'],
  ALLOWED_ATTR: ['src', 'alt', 'href', 'title', 'class'],
  FORBID_SCRIPT: true,
  STRIP_COMMENTS: true
});
```

### 2. BROKEN FILE IMPORT SYSTEM (HIGH PRIORITY)

#### Current Broken Implementation
**File**: `src/components/BookReader.vue`  
**Lines**: 153-177  
**Issue**: Browser security prevents file path access

```typescript
// CURRENT BROKEN CODE (Lines 163-166)
alert('Book file import requires file dialog implementation in main process.');
contentError.value = 'Book import requires using the "Import Book" button in the Books view.';
```

#### COMPLETE FIX IMPLEMENTATION

**Step 1**: Add IPC handler in `electron/main/api/books-api.ts`
```typescript
// Add this new handler
ipcMain.handle('books:selectAndImportFile', async (event, bookId: number) => {
  try {
    const result = await dialog.showOpenDialog({
      properties: ['openFile'],
      filters: [
        { name: 'eBooks', extensions: ['epub', 'pdf', 'mobi', 'azw3', 'fb2', 'cbz'] },
        { name: 'EPUB Files', extensions: ['epub'] },
        { name: 'PDF Files', extensions: ['pdf'] },
        { name: 'All Files', extensions: ['*'] }
      ],
      title: 'Select Book File to Import'
    });

    if (result.canceled || !result.filePaths[0]) {
      return { success: false, message: 'Import cancelled by user' };
    }

    const filePath = result.filePaths[0];
    console.log(`Importing book file: ${filePath}`);

    // Parse the book
    const parser = BookParser.getInstance();
    const parsedBook = await parser.parseBook(filePath);

    // Store the content
    await bookContentStorage.storeBookContent(bookId, parsedBook);

    // Update book record
    await updateBook(bookId, {
      has_content: true,
      book_file_path: filePath,
      book_file_format: parsedBook.metadata.format,
      file_size: parsedBook.originalFile.length,
      page_count: parsedBook.totalPages
    });

    return { 
      success: true, 
      message: 'Book imported successfully',
      totalPages: parsedBook.totalPages,
      format: parsedBook.metadata.format
    };

  } catch (error) {
    console.error('Book import failed:', error);
    return { 
      success: false, 
      message: `Import failed: ${error.message}` 
    };
  }
});
```

**Step 2**: Update API bridge in `electron/preload/api-bridge.ts`
```typescript
// Add to bookContent object
selectAndImportFile: (bookId: number) => 
  ipcRenderer.invoke('books:selectAndImportFile', bookId),
```

**Step 3**: Update TypeScript definitions in `src/types/electron-api.d.ts`
```typescript
// Add to BookContentAPI interface
selectAndImportFile(bookId: number): Promise<{
  success: boolean;
  message: string;
  totalPages?: number;
  format?: string;
}>;
```

**Step 4**: Fix BookReader.vue implementation
```typescript
// Replace the broken processBookFile function
const importBookFile = async () => {
  try {
    isLoadingContent.value = true;
    contentError.value = '';
    
    const result = await window.db.bookContent.selectAndImportFile(props.book.id!);
    
    if (result.success) {
      hasBookFile.value = true;
      totalPages.value = result.totalPages || 100;
      currentPage.value = 1;
      
      // Load the first page
      await loadPageContent();
      
      // Start reading session
      const session = await window.db.bookContent.startSession({
        bookId: props.book.id!,
        startPage: 1
      });
      readingSessionId.value = session.sessionId;
      
    } else {
      contentError.value = result.message;
    }
    
  } catch (error) {
    console.error('Failed to import book:', error);
    contentError.value = 'Failed to import book file. Please try again.';
  } finally {
    isLoadingContent.value = false;
  }
};

// Update the import button click handler
const triggerFileImport = () => {
  importBookFile();
};
```

**Step 5**: Update the template
```vue
<!-- Replace the broken file input with a proper button -->
<button class="import-button" @click="triggerFileImport">
  Choose File
</button>
<!-- Remove the hidden file input entirely -->
```

### 3. PDF.js CONFIGURATION (HIGH PRIORITY)

#### Current Issue
**File**: `electron/main/lib/book-parser.ts`  
**Line**: 9  
**Problem**: Incorrect worker configuration

```typescript
// CURRENT BROKEN CODE
GlobalWorkerOptions.workerSrc = require('pdfjs-dist/build/pdf.worker.entry');
```

#### COMPLETE FIX

**Step 1**: Update `vite.config.ts`
```typescript
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import electron from 'vite-plugin-electron/simple'
import renderer from 'vite-plugin-electron-renderer'
import pkg from './package.json'

export default defineConfig(({ command }) => {
  const isServe = command === 'serve'
  const isBuild = command === 'build'

  return {
    base: isBuild ? './' : '/',
    plugins: [
      vue(),
      electron({
        main: {
          entry: 'electron/main/index.ts',
          vite: {
            build: {
              rollupOptions: {
                external: Object.keys('dependencies' in pkg ? pkg.dependencies : {}),
              },
            },
          },
        },
        preload: {
          input: 'electron/preload/index.ts',
          vite: {
            build: {
              rollupOptions: {
                external: Object.keys('dependencies' in pkg ? pkg.dependencies : {}),
              },
            },
          },
        },
        renderer: {},
      }),
      renderer(),
    ],
    // ADD THESE CONFIGURATIONS FOR PDF.js
    optimizeDeps: {
      include: ['pdfjs-dist'],
      exclude: ['pdfjs-dist/build/pdf.worker.js']
    },
    define: {
      global: 'globalThis'
    },
    worker: {
      format: 'es'
    },
    build: {
      rollupOptions: {
        output: {
          manualChunks: {
            'pdfjs': ['pdfjs-dist']
          }
        }
      }
    }
  }
})
```

**Step 2**: Fix worker configuration in `book-parser.ts`
```typescript
// Replace line 9 with:
import { GlobalWorkerOptions } from 'pdfjs-dist';

// For Electron main process, use the bundled worker
if (typeof window === 'undefined') {
  // Main process - use require for worker
  GlobalWorkerOptions.workerSrc = require.resolve('pdfjs-dist/build/pdf.worker.js');
} else {
  // Renderer process - use URL
  GlobalWorkerOptions.workerSrc = new URL(
    'pdfjs-dist/build/pdf.worker.min.js',
    import.meta.url
  ).toString();
}
```

### 4. EPUB.js INTEGRATION (MEDIUM PRIORITY)

#### Current Implementation Issues
**File**: `electron/main/lib/book-parser.ts`  
**Lines**: 89-150  
**Issues**: 
- No proper error handling
- Missing CFI (Canonical Fragment Identifier) support
- Incomplete chapter processing

#### COMPLETE EPUB PARSER FIX

```typescript
// Replace the parseEpub method entirely
private async parseEpub(filePath: string, fileBuffer: Buffer, fileHash: string): Promise<ParsedBook> {
  let book: any = null;
  
  try {
    // Create book instance
    book = EPub(fileBuffer);
    await book.ready;

    // Validate EPUB structure
    if (!book.packaging || !book.packaging.metadata) {
      throw new Error('Invalid EPUB file: Missing metadata');
    }

    // Extract metadata with validation
    const metadata: BookMetadata = {
      title: book.packaging.metadata.title || 'Unknown Title',
      author: Array.isArray(book.packaging.metadata.creator) 
        ? book.packaging.metadata.creator.join(', ')
        : book.packaging.metadata.creator || 'Unknown Author',
      publisher: book.packaging.metadata.publisher,
      publishedDate: book.packaging.metadata.pubdate,
      language: book.packaging.metadata.language,
      isbn: book.packaging.metadata.identifier,
      description: book.packaging.metadata.description,
      format: 'epub',
      coverUrl: book.cover
    };

    // Get navigation with error handling
    let navigation;
    try {
      navigation = await book.loaded.navigation;
    } catch (navError) {
      console.warn('Navigation loading failed, using spine:', navError);
      navigation = { toc: [] };
    }

    const chapters: ChapterInfo[] = [];
    const pages: PageContent[] = [];
    
    // Process table of contents
    let chapterOrder = 0;
    const processNavItem = async (item: any, level: number = 0, parent?: string) => {
      try {
        const chapter: ChapterInfo = {
          id: item.id || `chapter-${chapterOrder}`,
          title: item.label || item.title || `Chapter ${chapterOrder + 1}`,
          href: item.href,
          order: chapterOrder++,
          level,
          parent
        };
        chapters.push(chapter);

        // Process subitems recursively
        if (item.subitems && Array.isArray(item.subitems)) {
          for (const subitem of item.subitems) {
            await processNavItem(subitem, level + 1, chapter.id);
          }
        }
      } catch (itemError) {
        console.warn(`Failed to process navigation item:`, itemError);
      }
    };

    // Process navigation items
    if (navigation.toc && Array.isArray(navigation.toc)) {
      for (const item of navigation.toc) {
        await processNavItem(item);
      }
    }

    // Process spine items (reading order)
    const spine = await book.loaded.spine;
    let pageNumber = 1;

    for (const item of spine.items) {
      try {
        const section = await item.load(book.load.bind(book));
        
        if (!section || !section.document) {
          console.warn(`Failed to load section: ${item.href}`);
          continue;
        }

        // Extract and sanitize content
        const textContent = section.document.body?.textContent || '';
        const rawHtml = section.document.body?.innerHTML || '';
        
        // Sanitize HTML content
        const htmlContent = purify.sanitize(rawHtml, {
          ALLOWED_TAGS: ['p', 'div', 'span', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'em', 'strong', 'i', 'b', 'u', 'br', 'img', 'a', 'ul', 'ol', 'li', 'blockquote', 'pre', 'code'],
          ALLOWED_ATTR: ['src', 'alt', 'href', 'title', 'class', 'id'],
          FORBID_SCRIPT: true,
          STRIP_COMMENTS: true
        });

        // Find associated chapter
        const chapter = chapters.find(ch => ch.href === item.href);

        // Create page content
        const pageContent: PageContent = {
          pageNumber: pageNumber++,
          chapterId: chapter?.id,
          contentHtml: htmlContent,
          contentText: textContent
        };

        pages.push(pageContent);

      } catch (sectionError) {
        console.warn(`Failed to process spine item ${item.href}:`, sectionError);
        // Continue processing other sections
      }
    }

    // Validate we have content
    if (pages.length === 0) {
      throw new Error('No readable content found in EPUB file');
    }

    return {
      metadata,
      chapters,
      pages,
      tableOfContents: navigation.toc,
      totalPages: pages.length,
      fileHash,
      originalFile: fileBuffer
    };

  } catch (error) {
    console.error('EPUB parsing failed:', error);
    throw new Error(`Failed to parse EPUB: ${error.message}`);
  } finally {
    // Cleanup
    if (book && typeof book.destroy === 'function') {
      try {
        book.destroy();
      } catch (cleanupError) {
        console.warn('EPUB cleanup failed:', cleanupError);
      }
    }
  }
}
```

### 5. DATABASE ARCHITECTURE FIX (MEDIUM PRIORITY)

#### Current Problem: BLOB Storage
**File**: `electron/main/database/database.ts`  
**Line**: 277  
**Issue**: Storing entire book files as BLOBs will cause database bloat

#### SOLUTION: Hybrid File System + Database

**Step 1**: Create new schema migration
```sql
-- Add to handleDatabaseMigrations function
-- Replace BLOB storage with file path storage
ALTER TABLE book_content ADD COLUMN file_path TEXT;
ALTER TABLE book_content ADD COLUMN compression_type TEXT DEFAULT 'none';
ALTER TABLE book_content ADD COLUMN extraction_version INTEGER DEFAULT 1;

-- Create content chunks table for large books
CREATE TABLE IF NOT EXISTS book_content_chunks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    book_id INTEGER NOT NULL,
    chunk_number INTEGER NOT NULL,
    chunk_type TEXT NOT NULL,
    content_data TEXT,
    compression_type TEXT DEFAULT 'gzip',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE,
    UNIQUE(book_id, chunk_number, chunk_type)
);
```

**Step 2**: Update book content storage
```typescript
// In book-content-storage.ts
import * as path from 'path';
import * as fs from 'fs/promises';
import { app } from 'electron';
import * as zlib from 'zlib';

const getBookStoragePath = (bookId: number): string => {
  const userDataPath = app.getPath('userData');
  return path.join(userDataPath, 'books', 'content', bookId.toString());
};

const storeBookContent = async (bookId: number, parsedBook: ParsedBook): Promise<void> => {
  const storagePath = getBookStoragePath(bookId);
  
  // Create directory structure
  await fs.mkdir(storagePath, { recursive: true });
  await fs.mkdir(path.join(storagePath, 'pages'), { recursive: true });
  await fs.mkdir(path.join(storagePath, 'images'), { recursive: true });
  
  // Store original file
  const originalPath = path.join(storagePath, `original.${parsedBook.metadata.format}`);
  await fs.writeFile(originalPath, parsedBook.originalFile);
  
  // Store metadata
  const metadataPath = path.join(storagePath, 'metadata.json');
  await fs.writeFile(metadataPath, JSON.stringify({
    metadata: parsedBook.metadata,
    chapters: parsedBook.chapters,
    tableOfContents: parsedBook.tableOfContents,
    totalPages: parsedBook.totalPages,
    fileHash: parsedBook.fileHash
  }, null, 2));
  
  // Store pages as individual files
  for (const page of parsedBook.pages) {
    const pageFile = path.join(storagePath, 'pages', `page-${page.pageNumber.toString().padStart(4, '0')}.html`);
    await fs.writeFile(pageFile, page.contentHtml || '');
  }
  
  // Update database with file path instead of BLOB
  await runAsync(db, `
    INSERT OR REPLACE INTO book_content (
      book_id, format, file_path, file_hash, total_pages, total_chapters,
      table_of_contents, metadata, created_at, updated_at
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
  `, [
    bookId,
    parsedBook.metadata.format,
    originalPath,
    parsedBook.fileHash,
    parsedBook.totalPages,
    parsedBook.chapters.length,
    JSON.stringify(parsedBook.tableOfContents),
    JSON.stringify(parsedBook.metadata)
  ]);
};
```

This implementation guide provides complete, production-ready solutions for all critical issues in the book reading system. The security fixes must be implemented immediately, followed by the file import system and rendering library configurations.

## 📚 COMPREHENSIVE LIBRARY COMPARISON & RECOMMENDATIONS

### PDF Rendering Libraries - DETAILED ANALYSIS

#### 1. PDF.js (Mozilla) - ⭐ RECOMMENDED
**Current Status**: ✅ Already installed (`pdfjs-dist: ^4.0.379`)
**GitHub**: https://github.com/mozilla/pdf.js
**Bundle Size**: ~2.8MB (large but feature-complete)

**Pros**:
- Industry standard, used by Firefox and Chrome
- Excellent security (sandboxed rendering)
- Full PDF specification support
- Text extraction and search capabilities
- Annotation support
- Active development and maintenance
- Works in Electron without native dependencies

**Cons**:
- Large bundle size
- Complex setup and configuration
- Memory intensive for large PDFs
- Worker configuration required

**Production Readiness**: ⭐⭐⭐⭐⭐ (Excellent)
**Security**: ⭐⭐⭐⭐⭐ (Excellent - sandboxed)
**Performance**: ⭐⭐⭐⭐ (Good - can be optimized)

**Implementation Complexity**: Medium-High
```typescript
// Proper PDF.js setup for Electron
import { getDocument, GlobalWorkerOptions } from 'pdfjs-dist';

// Configure worker for Electron
GlobalWorkerOptions.workerSrc = new URL(
  'pdfjs-dist/build/pdf.worker.min.js',
  import.meta.url
).toString();

const loadPDF = async (filePath: string) => {
  const loadingTask = getDocument(filePath);
  const pdf = await loadingTask.promise;
  return pdf;
};
```

#### 2. react-pdf (wojtekmaj) - Alternative Option
**GitHub**: https://github.com/wojtekmaj/react-pdf
**Bundle Size**: ~1.2MB (smaller than PDF.js)

**Pros**:
- Easier setup than raw PDF.js
- React-specific optimizations
- Good documentation
- Smaller bundle size

**Cons**:
- React-specific (not ideal for Vue)
- Less features than raw PDF.js
- Dependency on React ecosystem
- Limited customization options

**Production Readiness**: ⭐⭐⭐⭐ (Good)
**Recommendation**: ❌ Not suitable for Vue.js project

#### 3. PDFObject - Simple Embedding
**GitHub**: https://github.com/pipwerks/PDFObject
**Bundle Size**: ~8KB (tiny)

**Pros**:
- Extremely lightweight
- Simple implementation
- No dependencies

**Cons**:
- Relies on browser's built-in PDF viewer
- No programmatic control
- Limited features
- No text extraction

**Production Readiness**: ⭐⭐ (Basic)
**Recommendation**: ❌ Too limited for book reading app

### EPUB Rendering Libraries - DETAILED ANALYSIS

#### 1. epub.js (FuturePress) - ⭐ RECOMMENDED
**Current Status**: ✅ Already installed (`epubjs: ^0.3.93`)
**GitHub**: https://github.com/futurepress/epub.js
**Bundle Size**: ~400KB

**Pros**:
- Most mature EPUB library
- Full EPUB3 specification support
- CFI (Canonical Fragment Identifier) support
- Annotation and bookmark capabilities
- Theming and customization
- Pagination and flow control
- Search functionality
- Active community

**Cons**:
- Complex API
- Some maintenance concerns (slower updates)
- Memory usage with large books
- CSS conflicts possible

**Production Readiness**: ⭐⭐⭐⭐⭐ (Excellent)
**Security**: ⭐⭐⭐⭐ (Good with proper sanitization)
**Performance**: ⭐⭐⭐⭐ (Good)

**Implementation Example**:
```typescript
import ePub from 'epubjs';

const loadEPUB = async (filePath: string) => {
  const book = ePub(filePath);
  const rendition = book.renderTo('epub-container', {
    width: '100%',
    height: '600px'
  });

  await rendition.display();
  return { book, rendition };
};
```

#### 2. foliate-js - Modern Alternative
**GitHub**: https://github.com/johnfactotum/foliate-js
**Bundle Size**: ~200KB (smaller)

**Pros**:
- Modern, lightweight implementation
- Better performance than epub.js
- Cleaner API design
- Active development
- Used by Foliate desktop app

**Cons**:
- Newer library (less battle-tested)
- Smaller community
- Limited documentation
- Fewer features than epub.js

**Production Readiness**: ⭐⭐⭐ (Good but newer)
**Recommendation**: 🤔 Consider for future migration

#### 3. readium-js - W3C Standard
**GitHub**: https://github.com/readium/readium-js
**Bundle Size**: ~800KB

**Pros**:
- W3C Readium standard implementation
- Professional-grade features
- Excellent EPUB3 support

**Cons**:
- Complex setup
- Large bundle size
- Overkill for most applications
- Steep learning curve

**Production Readiness**: ⭐⭐⭐⭐ (Good but complex)
**Recommendation**: ❌ Too complex for this project

### Security Libraries - CRITICAL REQUIREMENTS

#### 1. DOMPurify - ⭐ REQUIRED IMMEDIATELY
**GitHub**: https://github.com/cure53/DOMPurify
**Bundle Size**: ~45KB

**Installation**:
```bash
npm install dompurify @types/dompurify
```

**Why Critical**:
- Prevents XSS attacks from malicious EPUB files
- Industry standard for HTML sanitization
- Works in both browser and Node.js environments
- Configurable sanitization rules

**Implementation**:
```typescript
import DOMPurify from 'dompurify';

const sanitizeBookContent = (html: string) => {
  return DOMPurify.sanitize(html, {
    ALLOWED_TAGS: ['p', 'div', 'span', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'em', 'strong', 'i', 'b', 'u', 'br', 'img', 'a', 'ul', 'ol', 'li', 'blockquote', 'pre', 'code'],
    ALLOWED_ATTR: ['src', 'alt', 'href', 'title', 'class', 'id'],
    FORBID_SCRIPT: true,
    STRIP_COMMENTS: true
  });
};
```

### Additional Utility Libraries

#### 1. jszip - ✅ Already Installed
**Purpose**: EPUB file extraction (EPUB is ZIP format)
**Status**: Already in package.json
**Version**: `^3.10.1`

#### 2. crypto-js - ✅ Already Installed
**Purpose**: File hashing for duplicate detection
**Status**: Already in package.json
**Version**: `^4.2.0`

#### 3. fflate - Potential Upgrade
**GitHub**: https://github.com/101arrowz/fflate
**Purpose**: Faster ZIP/compression library
**Bundle Size**: ~8KB (much smaller than jszip)

**Recommendation**: Consider for future optimization

### Performance Optimization Libraries

#### 1. comlink - Web Worker Communication
**GitHub**: https://github.com/GoogleChromeLabs/comlink
**Purpose**: Offload PDF/EPUB parsing to web workers
**Bundle Size**: ~2.5KB

**Benefits**:
- Non-blocking UI during book parsing
- Better performance for large files
- Cleaner worker communication

**Implementation Example**:
```typescript
// worker.ts
import * as Comlink from 'comlink';
import { BookParser } from './book-parser';

const api = {
  parseBook: async (filePath: string) => {
    const parser = BookParser.getInstance();
    return await parser.parseBook(filePath);
  }
};

Comlink.expose(api);

// main.ts
import * as Comlink from 'comlink';

const worker = new Worker(new URL('./worker.ts', import.meta.url));
const api = Comlink.wrap(worker);

const parseBookInWorker = async (filePath: string) => {
  return await api.parseBook(filePath);
};
```

#### 2. virtual-list-vue3 - Virtual Scrolling
**GitHub**: https://github.com/tangbc/vue-virtual-scroll-list
**Purpose**: Handle large books with many pages
**Bundle Size**: ~15KB

**Benefits**:
- Render only visible pages
- Constant memory usage regardless of book size
- Smooth scrolling performance

### FINAL LIBRARY RECOMMENDATIONS

#### Immediate Installation Required (Security Critical):
```bash
npm install dompurify @types/dompurify
```

#### Optional Performance Enhancements:
```bash
npm install comlink virtual-list-vue3
```

#### Future Considerations:
```bash
npm install fflate  # Replace jszip for better performance
npm install foliate-js  # Consider as epub.js alternative
```

### Implementation Priority Order

1. **CRITICAL (Day 1)**: Install and implement DOMPurify
2. **HIGH (Day 2-3)**: Fix PDF.js worker configuration
3. **HIGH (Day 4-5)**: Implement proper file import system
4. **MEDIUM (Week 2)**: Add virtual scrolling for performance
5. **LOW (Future)**: Consider library upgrades (foliate-js, fflate)

### Bundle Size Impact Analysis

**Current Dependencies**:
- pdfjs-dist: ~2.8MB
- epubjs: ~400KB
- jszip: ~100KB
- crypto-js: ~50KB

**After Security Fix**:
- +dompurify: ~45KB
- **Total**: ~3.4MB

**After Performance Optimizations**:
- +comlink: ~2.5KB
- +virtual-list-vue3: ~15KB
- **Total**: ~3.42MB

**Impact**: Minimal bundle size increase for critical security and performance improvements.

This comprehensive library analysis provides the technical foundation for implementing a secure, performant, and maintainable book reading system in the Noti application.
